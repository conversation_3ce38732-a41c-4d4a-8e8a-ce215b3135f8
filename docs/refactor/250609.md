# Markdown LiveSync 重构计划文档

## 1. 重构目标

将 Markdown LiveSync 插件的预览功能从浏览器迁移到 VSCode 内置预览面板，提升用户体验和性能。

### 1.1 重构后功能描述

1. **核心预览功能**
   - 在 VSCode 编辑器中直接显示预览面板
   - 支持实时预览和自动更新
   - 支持分屏显示（编辑器和预览并排）
   - 支持预览面板的显示/隐藏切换
   - 支持预览独立为新窗口
     - 支持拖拽预览面板到新窗口
     - 支持预览窗口的独立调整大小
     - 支持多显示器显示
     - 支持预览窗口的独立主题设置
     - 支持预览窗口的独立快捷键配置
   - 支持单窗口多文件预览
     - 支持在同一个预览窗口中切换不同的 Markdown 文件
     - 支持预览窗口标题栏显示当前文件名
     - 支持预览窗口标签页切换
     - 支持预览历史记录导航
     - 支持预览窗口状态保持

2. **Markdown 渲染功能**
   - 支持标准 Markdown 语法渲染
   - 支持 GitHub Flavored Markdown
   - 支持数学公式渲染（KaTeX）
   - 支持代码块语法高亮
   - 支持表格渲染和样式
   - 支持任务列表和复选框

3. **目录导航功能**
   - 自动生成文档目录
   - 支持目录可以按照级别折叠/展开，默认折叠到第 2 级，可以配置折叠级别，按钮样式参考当前实现
   - 支持目录项快速跳转
   - 支持目录项高亮显示
   - 支持自定义目录样式

4. **图表渲染功能**
   - 支持 Mermaid 图表实时渲染
   - 支持流程图、序列图、甘特图等
   - 支持图表交互和缩放
   - 支持图表主题切换
   - 支持图表导出功能
   - 支持图表全屏显示模式
   - 支持全屏模式下的图表编辑
   - 支持全屏模式下的图表导出

5. **同步功能**
   - 实时同步编辑器内容到预览
   - 同步光标位置和滚动位置
   - 支持双向同步（预览点击跳转到编辑器）
   - 支持同步状态指示

6. **主题和样式**
   - 自动适配 VSCode 主题
   - 支持自定义 CSS 样式
   - 支持自定义字体设置
   - 支持暗色/亮色主题切换
   - 支持自定义代码高亮主题

7. **资源处理**
   - 支持本地图片预览
   - 支持网络图片加载
   - 支持图片缩放和查看
   - 支持相对路径和绝对路径
   - 支持资源文件缓存

8. **交互功能**
   - 支持预览内容复制
   - 支持链接点击跳转
   - 支持图片右键菜单
   - 支持代码块复制
   - 支持表格内容选择

9. **性能优化**
   - 支持大文件分块渲染
   - 支持增量更新
   - 支持资源懒加载
   - 支持渲染缓存
   - 支持后台预渲染

10. **扩展功能**
    - 支持自定义 Markdown 扩展
    - 支持自定义渲染器
    - 支持自定义快捷键
    - 支持自定义命令
    - 支持插件 API

11. **调试功能**
    - 支持渲染调试
    - 支持性能分析
    - 支持错误日志
    - 支持状态监控
    - 支持调试工具

12. **配置功能**
    - 支持全局配置
    - 支持工作区配置
    - 支持文件级配置
    - 支持配置导入导出
    - 支持配置同步

## 2. 重构步骤

### 第一阶段：基础架构重构（2周）

1. **移除浏览器相关代码**
   - 删除 `src/browser/browserIntegration.ts`
   - 删除 `webview/` 目录
   - 移除 `package.json` 中的浏览器相关依赖
   - 清理 HTTP 服务器相关代码

2. **创建新的预览面板模块**
   - 创建 `src/preview/MarkdownPreviewPanel.ts`
   - 实现基本的 Webview 面板创建和管理
   - 实现单窗口多文件预览支持
   - 实现预览窗口的拖拽和独立显示

3. **调整配置项**
   - 移除浏览器相关配置
   - 添加预览窗口相关配置
   - 添加主题和样式配置
   - 添加性能相关配置

### 第二阶段：核心功能迁移（2周）

1. **实现 Markdown 渲染**
   - 迁移 `MarkdownProcessor` 类到新架构
   - 实现 HTML 生成功能
   - 添加资源路径处理
   - 实现代码高亮支持
   - 实现数学公式渲染

2. **实现目录导航**
   - 创建 `src/preview/TocProvider.ts`
   - 实现目录生成和渲染
   - 实现目录折叠功能
     - 支持按级别折叠/展开
     - 默认折叠到第 2 级
     - 支持配置折叠级别
     - 实现折叠按钮样式
   - 实现目录交互功能
     - 支持目录项快速跳转
     - 支持目录项高亮显示
     - 支持目录状态保持
   - 实现目录样式自定义
   - 添加目录配置选项

3. **实现实时同步**
   - 添加文档变更监听
   - 实现光标位置同步
   - 添加滚动同步功能
   - 实现双向同步支持
   - 添加同步状态指示

### 第三阶段：扩展功能迁移（2周）

1. **Mermaid 图表支持**
   - 迁移 Mermaid 渲染功能
   - 优化图表加载性能
   - 添加图表交互功能
   - 实现图表主题切换
   - 实现图表全屏模式

2. **主题和样式支持**
   - 实现 VSCode 主题适配
   - 添加自定义样式支持
   - 优化暗色/亮色主题切换
   - 实现字体自定义
   - 实现代码高亮主题

3. **资源处理优化**
   - 实现本地图片预览
   - 支持网络图片加载
   - 实现图片缩放功能
   - 添加资源文件缓存
   - 优化资源加载性能

### 第四阶段：性能优化（1周）

1. **渲染性能优化**
   - 实现大文件分块渲染
   - 添加增量更新支持
   - 优化资源懒加载
   - 实现渲染缓存
   - 添加后台预渲染

2. **内存优化**
   - 优化资源管理
   - 实现内存使用监控
   - 添加自动清理机制
   - 优化缓存策略
   - 实现资源释放

3. **交互优化**
   - 优化响应速度
   - 添加加载状态提示
   - 优化错误处理
   - 添加操作反馈
   - 优化用户体验

### 第五阶段：测试和文档（1周）

1. **单元测试**
   - 编写预览面板测试
   - 编写目录导航测试
   - 编写渲染功能测试
   - 编写同步功能测试
   - 编写性能测试

2. **集成测试**
   - 测试完整预览流程
   - 测试多文件切换
   - 测试主题切换
   - 测试性能指标
   - 测试兼容性

3. **文档更新**
   - 更新使用文档
   - 编写开发文档
   - 添加示例代码
   - 更新配置说明
   - 编写常见问题

## 3. 详细实现计划

### 3.1 第一阶段：基础架构重构

#### 3.1.1 移除浏览器相关代码

```typescript
// 1. 删除文件
- src/browser/browserIntegration.ts
- webview/preview.js
- webview/markdown.css
- webview/preview.html

// 2. 修改 package.json
{
  "dependencies": {
    // 移除以下依赖
    "express": "^4.17.1",
    "ws": "^8.13.0",
    "open": "^8.4.0"
  }
}

// 3. 清理 HTTP 服务器代码
- src/server/markdownServer.ts
- src/server/websocketServer.ts
```

#### 3.1.2 创建预览面板模块

```typescript
// src/preview/MarkdownPreviewPanel.ts
import * as vscode from 'vscode';
import { MarkdownProcessor } from '../markdown/markdownProcessor';
import { TocProvider } from './TocProvider';

export class MarkdownPreviewPanel {
  private static instance: MarkdownPreviewPanel;
  private panel: vscode.WebviewPanel;
  private currentDocument: vscode.TextDocument | null = null;
  private markdownProcessor: MarkdownProcessor;
  private tocProvider: TocProvider;
  private disposables: vscode.Disposable[] = [];

  private constructor() {
    this.markdownProcessor = new MarkdownProcessor();
    this.tocProvider = new TocProvider();
  }

  public static getInstance(): MarkdownPreviewPanel {
    if (!MarkdownPreviewPanel.instance) {
      MarkdownPreviewPanel.instance = new MarkdownPreviewPanel();
    }
    return MarkdownPreviewPanel.instance;
  }

  public async show(document: vscode.TextDocument): Promise<void> {
    if (!this.panel) {
      this.createPanel();
    }
    this.currentDocument = document;
    await this.updateContent();
  }

  private createPanel(): void {
    this.panel = vscode.window.createWebviewPanel(
      'markdownPreview',
      'Markdown预览',
      vscode.ViewColumn.Beside,
      {
        enableScripts: true,
        retainContextWhenHidden: true,
        localResourceRoots: [
          vscode.Uri.file(path.join(this.context.extensionPath, 'media'))
        ]
      }
    );

    this.setupEventListeners();
  }

  private setupEventListeners(): void {
    // 面板关闭事件
    this.panel.onDidDispose(() => {
      this.dispose();
    });

    // 文档变更事件
    this.disposables.push(
      vscode.workspace.onDidChangeTextDocument(e => {
        if (e.document === this.currentDocument) {
          this.updateContent();
        }
      })
    );

    // 光标位置变化事件
    this.disposables.push(
      vscode.window.onDidChangeTextEditorSelection(e => {
        if (e.textEditor.document === this.currentDocument) {
          this.syncCursorPosition(e.selections[0].active);
        }
      })
    );

    // Webview 消息处理
    this.panel.webview.onDidReceiveMessage(
      message => {
        switch (message.type) {
          case 'ready':
            this.updateContent();
            break;
          case 'click':
            this.handleClick(message);
            break;
          case 'scroll':
            this.handleScroll(message);
            break;
        }
      },
      null,
      this.disposables
    );
  }

  private async updateContent(): Promise<void> {
    if (!this.currentDocument || !this.panel) {
      return;
    }

    const content = this.currentDocument.getText();
    const html = this.markdownProcessor.convertToHtml(content);
    const toc = this.tocProvider.generateToc(this.currentDocument);

    this.panel.webview.html = this.getWebviewContent(html, toc);
    this.panel.title = `预览: ${path.basename(this.currentDocument.fileName)}`;
  }

  private getWebviewContent(html: string, toc: TocItem[]): string {
    return `
      <!DOCTYPE html>
      <html>
        <head>
          <meta charset="UTF-8">
          <meta name="viewport" content="width=device-width, initial-scale=1.0">
          <link rel="stylesheet" href="${this.panel.webview.asWebviewUri(
            vscode.Uri.file(path.join(this.context.extensionPath, 'media', 'preview.css'))
          )}">
          <script src="https://cdn.jsdelivr.net/npm/mermaid@11.6.0/dist/mermaid.min.js"></script>
        </head>
        <body>
          <div class="container">
            <div id="toc-container">
              ${this.renderToc(toc)}
            </div>
            <div id="content-container">
              ${html}
            </div>
          </div>
          <script>
            // 初始化
            (function() {
              // 发送就绪消息
              vscode.postMessage({ type: 'ready' });
              
              // 初始化 Mermaid
              mermaid.initialize({ startOnLoad: true });
              
              // 设置事件监听
              setupEventListeners();
            })();
          </script>
        </body>
      </html>
    `;
  }

  public dispose(): void {
    this.disposables.forEach(d => d.dispose());
    this.panel?.dispose();
    this.panel = null;
    this.currentDocument = null;
  }
}
```

#### 3.1.3 调整配置项

```json
// package.json
{
  "contributes": {
    "configuration": {
      "properties": {
        "markdown-livesync.preview": {
          "type": "object",
          "properties": {
            "defaultView": {
              "type": "string",
              "enum": ["side", "window"],
              "default": "side",
              "description": "预览面板的默认显示位置"
            },
            "showToc": {
              "type": "boolean",
              "default": true,
              "description": "是否显示目录"
            },
            "syncScroll": {
              "type": "boolean",
              "default": true,
              "description": "是否同步滚动"
            }
          }
        },
        "markdown-livesync.theme": {
          "type": "object",
          "properties": {
            "fontSize": {
              "type": "number",
              "default": 14,
              "description": "预览字体大小"
            },
            "fontFamily": {
              "type": "string",
              "default": "",
              "description": "预览字体"
            },
            "lineHeight": {
              "type": "number",
              "default": 1.6,
              "description": "行高"
            }
          }
        },
        "markdown-livesync.performance": {
          "type": "object",
          "properties": {
            "chunkSize": {
              "type": "number",
              "default": 1000,
              "description": "分块渲染大小"
            },
            "cacheSize": {
              "type": "number",
              "default": 100,
              "description": "缓存大小"
            },
            "lazyLoad": {
              "type": "boolean",
              "default": true,
              "description": "是否启用懒加载"
            }
          }
        }
      }
    }
  }
}
```

### 3.2 第二阶段：核心功能迁移

#### 3.2.1 Markdown 渲染实现

```typescript
// src/markdown/MarkdownProcessor.ts
import * as MarkdownIt from 'markdown-it';
import * as hljs from 'highlight.js';
import * as katex from 'katex';

export class MarkdownProcessor {
  private md: MarkdownIt;
  
  constructor() {
    this.md = new MarkdownIt({
      html: true,
      linkify: true,
      typographer: true,
      highlight: (str, lang) => {
        if (lang && hljs.getLanguage(lang)) {
          try {
            return hljs.highlight(str, { language: lang }).value;
          } catch (__) {}
        }
        return '';
      }
    });

    this.setupPlugins();
  }

  private setupPlugins(): void {
    // 数学公式插件
    this.md.use(require('markdown-it-katex'));
    
    // 任务列表插件
    this.md.use(require('markdown-it-task-lists'));
    
    // 表格插件
    this.md.use(require('markdown-it-table'));
  }

  public convertToHtml(content: string): string {
    return this.md.render(content);
  }
}
```

#### 3.2.2 目录导航实现

```typescript
// src/preview/TocProvider.ts
import * as vscode from 'vscode';

export interface TocItem {
  level: number;
  text: string;
  line: number;
  children: TocItem[];
  isExpanded?: boolean;
}

export class TocProvider {
  private defaultCollapseLevel: number = 2;
  private config: vscode.WorkspaceConfiguration;

  constructor() {
    this.config = vscode.workspace.getConfiguration('markdown-livesync.toc');
    this.defaultCollapseLevel = this.config.get('defaultCollapseLevel', 2);
  }

  public generateToc(document: vscode.TextDocument): TocItem[] {
    const toc: TocItem[] = [];
    const lines = document.getText().split('\n');
    
    for (let i = 0; i < lines.length; i++) {
      const line = lines[i];
      const match = line.match(/^(#{1,6})\s+(.+)$/);
      
      if (match) {
        const level = match[1].length;
        const text = match[2];
        toc.push({
          level,
          text,
          line: i,
          children: [],
          isExpanded: level <= this.defaultCollapseLevel
        });
      }
    }
    
    return this.buildTocTree(toc);
  }

  private buildTocTree(items: TocItem[]): TocItem[] {
    const root: TocItem[] = [];
    const stack: TocItem[] = [];
    
    for (const item of items) {
      while (stack.length > 0 && stack[stack.length - 1].level >= item.level) {
        stack.pop();
      }
      
      if (stack.length === 0) {
        root.push(item);
      } else {
        stack[stack.length - 1].children.push(item);
      }
      
      stack.push(item);
    }
    
    return root;
  }

  public renderToc(toc: TocItem[]): string {
    return this.renderTocItems(toc, 0);
  }

  private renderTocItems(items: TocItem[], level: number): string {
    if (items.length === 0) {
      return '';
    }

    const html = items.map(item => {
      const hasChildren = item.children.length > 0;
      const toggleButton = hasChildren ? `
        <button class="toc-toggle ${item.isExpanded ? 'expanded' : ''}" 
                data-level="${item.level}"
                onclick="toggleTocItem(this)">
          <span class="toc-toggle-icon"></span>
        </button>
      ` : '';

      return `
        <div class="toc-item level-${level} ${item.isExpanded ? 'expanded' : 'collapsed'}">
          <div class="toc-item-header">
            ${toggleButton}
            <a href="#${this.slugify(item.text)}" 
               data-line="${item.line}"
               class="toc-item-link">
              ${item.text}
            </a>
          </div>
          ${hasChildren ? this.renderTocItems(item.children, level + 1) : ''}
        </div>
      `;
    }).join('');

    return `<div class="toc-level-${level}">${html}</div>`;
  }

  private slugify(text: string): string {
    return text
      .toLowerCase()
      .replace(/[^\w\u4e00-\u9fa5]+/g, '-')
      .replace(/(^-|-$)/g, '');
  }
}

// src/preview/toc.css
.toc-item {
  margin: 0;
  padding: 0;
  list-style: none;
}

.toc-item-header {
  display: flex;
  align-items: center;
  padding: 4px 8px;
  cursor: pointer;
}

.toc-toggle {
  width: 16px;
  height: 16px;
  margin-right: 4px;
  padding: 0;
  border: none;
  background: transparent;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
}

.toc-toggle-icon {
  width: 8px;
  height: 8px;
  border-right: 2px solid var(--vscode-foreground);
  border-bottom: 2px solid var(--vscode-foreground);
  transform: rotate(-45deg);
  transition: transform 0.2s;
}

.toc-toggle.expanded .toc-toggle-icon {
  transform: rotate(45deg);
}

.toc-item-link {
  color: var(--vscode-foreground);
  text-decoration: none;
  flex: 1;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.toc-item-link:hover {
  color: var(--vscode-textLink-foreground);
}

.toc-item.active > .toc-item-header > .toc-item-link {
  color: var(--vscode-textLink-activeForeground);
  font-weight: bold;
}

// src/preview/toc.js
function setupTocEvents() {
  // 目录项点击事件
  document.querySelectorAll('.toc-item-link').forEach(link => {
    link.addEventListener('click', (e) => {
      e.preventDefault();
      const line = parseInt(link.dataset.line);
      vscode.postMessage({
        type: 'toc-click',
        line: line
      });
    });
  });

  // 目录折叠/展开事件
  document.querySelectorAll('.toc-toggle').forEach(button => {
    button.addEventListener('click', (e) => {
      e.stopPropagation();
      toggleTocItem(button);
    });
  });
}

function toggleTocItem(button) {
  const item = button.closest('.toc-item');
  const isExpanded = item.classList.toggle('expanded');
  button.classList.toggle('expanded');
  
  // 保存折叠状态
  vscode.postMessage({
    type: 'toc-toggle',
    level: parseInt(button.dataset.level),
    isExpanded: isExpanded
  });
}

// 配置项
{
  "markdown-livesync.toc": {
    "defaultCollapseLevel": {
      "type": "number",
      "default": 2,
      "description": "目录默认折叠级别"
    },
    "showToggleButton": {
      "type": "boolean",
      "default": true,
      "description": "是否显示折叠按钮"
    },
    "highlightCurrentItem": {
      "type": "boolean",
      "default": true,
      "description": "是否高亮当前目录项"
    },
    "rememberCollapseState": {
      "type": "boolean",
      "default": true,
      "description": "是否记住目录折叠状态"
    }
  }
}
```

### 3.3 第三阶段：扩展功能迁移

#### 3.3.1 Mermaid 图表支持

```typescript
// src/preview/MermaidRenderer.ts
export class MermaidRenderer {
  private static instance: MermaidRenderer;
  
  private constructor() {
    this.initializeMermaid();
  }
  
  public static getInstance(): MermaidRenderer {
    if (!MermaidRenderer.instance) {
      MermaidRenderer.instance = new MermaidRenderer();
    }
    return MermaidRenderer.instance;
  }
  
  private async initializeMermaid() {
    await this.loadScript('https://cdn.jsdelivr.net/npm/mermaid@11.6.0/dist/mermaid.min.js');
    
    (window as any).mermaid.initialize({
      startOnLoad: true,
      theme: 'default',
      securityLevel: 'loose',
      flowchart: {
        useMaxWidth: true,
        htmlLabels: true
      }
    });
  }
  
  public async renderDiagrams() {
    const diagrams = document.querySelectorAll('.mermaid');
    for (const diagram of diagrams) {
      try {
        const id = `mermaid-${Math.random().toString(36).substr(2, 9)}`;
        const { svg } = await (window as any).mermaid.render(id, diagram.textContent || '');
        diagram.innerHTML = svg;
        
        // 添加全屏支持
        this.addFullscreenSupport(diagram);
      } catch (error) {
        console.error('Mermaid 渲染错误:', error);
        diagram.innerHTML = `<div class="mermaid-error">${error.message}</div>`;
      }
    }
  }

  private addFullscreenSupport(element: Element) {
    const button = document.createElement('button');
    button.className = 'mermaid-fullscreen';
    button.innerHTML = '⛶';
    button.onclick = () => this.toggleFullscreen(element);
    element.appendChild(button);
  }

  private toggleFullscreen(element: Element) {
    if (document.fullscreenElement) {
      document.exitFullscreen();
    } else {
      element.requestFullscreen();
    }
  }
}
```

### 3.4 第四阶段：性能优化

#### 3.4.1 渲染性能优化

```typescript
// src/preview/Renderer.ts
export class Renderer {
  private chunkSize: number;
  private cache: Map<string, string>;
  private cacheSize: number;
  
  constructor() {
    const config = vscode.workspace.getConfiguration('markdown-livesync.performance');
    this.chunkSize = config.get('chunkSize', 1000);
    this.cacheSize = config.get('cacheSize', 100);
    this.cache = new Map();
  }

  public async render(content: string): Promise<string> {
    // 检查缓存
    const cacheKey = this.getCacheKey(content);
    if (this.cache.has(cacheKey)) {
      return this.cache.get(cacheKey)!;
    }

    // 分块渲染
    const chunks = this.splitContent(content);
    const renderedChunks = await Promise.all(
      chunks.map(chunk => this.renderChunk(chunk))
    );

    const result = renderedChunks.join('');
    
    // 更新缓存
    this.updateCache(cacheKey, result);
    
    return result;
  }

  private splitContent(content: string): string[] {
    const chunks: string[] = [];
    const lines = content.split('\n');
    
    for (let i = 0; i < lines.length; i += this.chunkSize) {
      chunks.push(lines.slice(i, i + this.chunkSize).join('\n'));
    }
    
    return chunks;
  }

  private async renderChunk(chunk: string): Promise<string> {
    // 使用 Web Worker 进行渲染
    return new Promise((resolve) => {
      const worker = new Worker('renderWorker.js');
      worker.postMessage(chunk);
      worker.onmessage = (e) => resolve(e.data);
    });
  }

  private updateCache(key: string, value: string): void {
    if (this.cache.size >= this.cacheSize) {
      // 移除最旧的缓存
      const firstKey = this.cache.keys().next().value;
      this.cache.delete(firstKey);
    }
    this.cache.set(key, value);
  }

  private getCacheKey(content: string): string {
    return crypto.createHash('md5').update(content).digest('hex');
  }
}
```

### 3.5 第五阶段：测试和文档

#### 3.5.1 单元测试

```typescript
// src/test/preview.test.ts
import * as assert from 'assert';
import * as vscode from 'vscode';
import { MarkdownPreviewPanel } from '../preview/MarkdownPreviewPanel';

suite('Markdown Preview Test Suite', () => {
  test('创建预览面板', async () => {
    const panel = MarkdownPreviewPanel.getInstance();
    assert.ok(panel);
  });

  test('渲染 Markdown 内容', async () => {
    const panel = MarkdownPreviewPanel.getInstance();
    const document = await vscode.workspace.openTextDocument({
      content: '# 测试标题\n\n测试内容'
    });
    await panel.show(document);
    // 验证渲染结果
  });

  test('目录生成', async () => {
    const tocProvider = new TocProvider();
    const document = await vscode.workspace.openTextDocument({
      content: '# 标题1\n## 标题2\n### 标题3'
    });
    const toc = tocProvider.generateToc(document);
    assert.strictEqual(toc.length, 3);
  });
});
```

#### 3.5.2 性能测试

```typescript
// src/test/performance.test.ts
import * as assert from 'assert';
import { Renderer } from '../preview/Renderer';

suite('Performance Test Suite', () => {
  test('大文件渲染性能', async () => {
    const renderer = new Renderer();
    const largeContent = '# 测试\n'.repeat(10000);
    
    const startTime = Date.now();
    await renderer.render(largeContent);
    const endTime = Date.now();
    
    assert.ok(endTime - startTime < 2000); // 2秒内完成
  });

  test('缓存性能', async () => {
    const renderer = new Renderer();
    const content = '# 测试内容';
    
    // 首次渲染
    const firstRenderStart = Date.now();
    await renderer.render(content);
    const firstRenderTime = Date.now() - firstRenderStart;
    
    // 缓存渲染
    const cacheRenderStart = Date.now();
    await renderer.render(content);
    const cacheRenderTime = Date.now() - cacheRenderStart;
    
    assert.ok(cacheRenderTime < firstRenderTime / 10); // 缓存渲染至少快10倍
  });
});
```

## 4. 测试计划

### 4.1 单元测试

1. **预览面板测试**
   - 测试面板创建和销毁
   - 测试文档变更监听
   - 测试光标同步

2. **目录导航测试**
   - 测试目录生成
   - 测试目录树构建
   - 测试目录交互

3. **渲染功能测试**
   - 测试 Markdown 渲染
   - 测试 Mermaid 图表渲染
   - 测试代码高亮

### 4.2 集成测试

1. **功能集成测试**
   - 测试完整预览流程
   - 测试实时同步
   - 测试主题切换

2. **性能测试**
   - 测试大文件渲染
   - 测试实时更新性能
   - 测试内存占用

## 5. 发布计划

### 5.1 版本规划

1. **v1.0.0-alpha**
   - 基础预览功能
   - 目录导航
   - 实时同步

2. **v1.0.0-beta**
   - Mermaid 图表支持
   - 代码高亮
   - 主题支持

3. **v1.0.0**
   - 完整功能
   - 性能优化
   - 文档完善

### 5.2 发布检查清单

- [ ] 功能测试通过
- [ ] 性能测试达标
- [ ] 文档更新完成
- [ ] 示例代码准备
- [ ] 发布说明编写
- [ ] 版本号更新
- [ ] 打包发布

## 6. 回滚计划

### 6.1 回滚触发条件

1. 发现严重 bug
2. 性能问题
3. 用户反馈问题

### 6.2 回滚步骤

1. 停止新版本发布
2. 回退到上一个稳定版本
3. 通知用户
4. 收集问题反馈
5. 修复问题
6. 重新发布

## 7. 时间规划

1. **第一阶段**：2周
   - 基础架构重构
   - 移除旧代码

2. **第二阶段**：2周
   - 核心功能迁移
   - 基础功能测试

3. **第三阶段**：2周
   - 扩展功能迁移
   - 功能测试

4. **第四阶段**：1周
   - 性能优化
   - 文档更新

总计：7周

## 8. 风险评估

### 8.1 技术风险

1. **性能问题**
   - 风险：大文件渲染性能
   - 缓解：实现分块渲染

2. **兼容性问题**
   - 风险：VSCode 版本兼容
   - 缓解：明确版本要求

### 8.2 项目风险

1. **进度风险**
   - 风险：功能迁移延迟
   - 缓解：设置缓冲时间

2. **质量风险**
   - 风险：功能不完整
   - 缓解：加强测试

## 9. 后续计划

1. **功能增强**
   - 添加更多 Markdown 扩展
   - 优化渲染性能
   - 添加更多自定义选项

2. **维护计划**
   - 定期更新依赖
   - 修复用户反馈问题
   - 优化用户体验