# Markdown LiveSync 重构计划文档

## 1. 重构目标

将 Markdown LiveSync 插件的预览功能从浏览器迁移到 VSCode 内置预览面板，提升用户体验和性能。

### 1.1 重构后功能描述

1. **核心预览功能**
   - 在 VSCode 编辑器中直接显示预览面板
   - 支持实时预览和自动更新
   - 支持分屏显示（编辑器和预览并排）
   - 支持预览面板的显示/隐藏切换
   - 支持预览独立为新窗口
     - 支持拖拽预览面板到新窗口
     - 支持预览窗口的独立调整大小
     - 支持多显示器显示
     - 支持预览窗口的独立主题设置
     - 支持预览窗口的独立快捷键配置
   - 支持单窗口多文件预览
     - 支持在同一个预览窗口中切换不同的 Markdown 文件
     - 支持预览窗口标题栏显示当前文件名
     - 支持预览窗口标签页切换
     - 支持预览历史记录导航
     - 支持预览窗口状态保持

2. **Markdown 渲染功能**
   - 支持标准 Markdown 语法渲染
   - 支持 GitHub Flavored Markdown
   - 支持数学公式渲染（KaTeX）
   - 支持代码块语法高亮
   - 支持表格渲染和样式
   - 支持任务列表和复选框

3. **目录导航功能**
   - 自动生成文档目录
   - 支持目录可以按照级别折叠/展开，默认折叠到第 2 级，可以配置折叠级别，按钮样式参考当前实现
   - 支持目录项快速跳转
   - 支持目录项高亮显示
   - 支持自定义目录样式

4. **图表渲染功能**
   - 支持 Mermaid 图表实时渲染
   - 支持流程图、序列图、甘特图等
   - 支持图表交互和缩放
   - 支持图表主题切换
   - 支持图表导出功能
   - 支持图表全屏显示模式
   - 支持全屏模式下的图表编辑
   - 支持全屏模式下的图表导出

5. **同步功能**
   - 实时同步编辑器内容到预览
   - 同步光标位置和滚动位置
   - 支持双向同步（预览点击跳转到编辑器）
   - 支持同步状态指示

6. **主题和样式**
   - 自动适配 VSCode 主题
   - 支持自定义 CSS 样式
   - 支持自定义字体设置
   - 支持暗色/亮色主题切换
   - 支持自定义代码高亮主题

7. **资源处理**
   - 支持本地图片预览
   - 支持网络图片加载
   - 支持图片缩放和查看
   - 支持相对路径和绝对路径
   - 支持资源文件缓存

8. **交互功能**
   - 支持预览内容复制
   - 支持链接点击跳转
   - 支持图片右键菜单
   - 支持代码块复制
   - 支持表格内容选择

9. **性能优化**
   - 支持大文件分块渲染
   - 支持增量更新
   - 支持资源懒加载
   - 支持渲染缓存
   - 支持后台预渲染

10. **扩展功能**
    - 支持自定义 Markdown 扩展
    - 支持自定义渲染器
    - 支持自定义快捷键
    - 支持自定义命令
    - 支持插件 API

11. **调试功能**
    - 支持渲染调试
    - 支持性能分析
    - 支持错误日志
    - 支持状态监控
    - 支持调试工具

12. **配置功能**
    - 支持全局配置
    - 支持工作区配置
    - 支持文件级配置
    - 支持配置导入导出
    - 支持配置同步

## 2. 重构步骤

### 第零阶段：重构准备（1周）

1. **代码备份和分支管理**
   - 创建重构专用分支 `refactor/vscode-preview`
   - 备份当前稳定版本到 `backup/pre-refactor`
   - 建立回滚检查点

2. **依赖关系分析**
   - 分析当前模块间的依赖关系
   - 识别需要保留的核心逻辑
   - 制定模块迁移优先级

3. **兼容性测试基线**
   - 建立当前功能的测试基线
   - 记录现有功能的性能指标
   - 创建用户配置迁移脚本

4. **风险评估和应急预案**
   - 识别高风险重构点
   - 制定每个阶段的回滚策略
   - 准备用户通知和支持文档

### 第一阶段：基础架构重构（2周）

1. **渐进式移除浏览器相关代码**
   - **第1步**：创建功能开关，允许新旧系统并存
   - **第2步**：逐步禁用 `src/browser/browserIntegration.ts` 功能
   - **第3步**：保留 `webview/` 目录作为参考，暂不删除
   - **第4步**：标记 `package.json` 中的浏览器依赖为可选
   - **第5步**：重构 HTTP 服务器为可选组件

2. **创建新的预览面板模块**
   - **第1步**：创建 `src/preview/` 目录结构
   - **第2步**：实现基础 `MarkdownPreviewPanel.ts`
   - **第3步**：实现 Webview 面板生命周期管理
   - **第4步**：添加基本的内容渲染功能
   - **第5步**：实现文档切换和状态管理

3. **配置系统重构**
   - **第1步**：创建配置迁移工具
   - **第2步**：添加新配置项（保持向后兼容）
   - **第3步**：实现配置验证和默认值处理
   - **第4步**：添加配置变更监听机制

### 第二阶段：核心功能迁移（2.5周）

1. **Markdown 渲染系统重构**
   - **第1步**：适配现有 `MarkdownProcessor` 到 Webview 环境
   - **第2步**：重构 HTML 生成，移除浏览器特定代码
   - **第3步**：实现 Webview 资源路径处理机制
   - **第4步**：迁移代码高亮功能（考虑 VSCode 主题集成）
   - **第5步**：适配数学公式渲染到 Webview
   - **第6步**：添加渲染错误处理和降级机制

2. **目录导航系统重构**
   - **第1步**：创建 `src/preview/TocProvider.ts`
   - **第2步**：迁移现有目录生成逻辑
   - **第3步**：实现 Webview 环境下的目录渲染
   - **第4步**：重构目录折叠功能
     - 实现状态持久化
     - 添加键盘导航支持
     - 优化大文档性能
   - **第5步**：实现目录交互功能
     - 双向同步（编辑器 ↔ 目录）
     - 智能高亮当前位置
     - 平滑滚动动画
   - **第6步**：添加目录自定义选项
     - 可配置的显示级别
     - 自定义样式主题
     - 搜索和过滤功能

3. **实时同步系统重构**
   - **第1步**：重构文档变更监听机制
   - **第2步**：实现高精度光标位置同步
   - **第3步**：优化滚动同步算法
   - **第4步**：实现双向同步（预览点击跳转编辑器）
   - **第5步**：添加同步状态可视化指示
   - **第6步**：实现同步冲突检测和处理

4. **数据迁移和兼容性**
   - **第1步**：实现用户配置自动迁移
   - **第2步**：保持与旧版本的API兼容性
   - **第3步**：添加功能降级支持（旧VSCode版本）

### 第三阶段：扩展功能迁移（2.5周）

1. **Mermaid 图表系统重构**
   - **第1步**：分析现有 Mermaid 集成方式
   - **第2步**：重构为 Webview 兼容的实现
   - **第3步**：优化图表加载性能（懒加载、缓存）
   - **第4步**：实现图表交互功能（缩放、导出）
   - **第5步**：集成 VSCode 主题到图表样式
   - **第6步**：实现图表全屏模式和编辑功能
   - **第7步**：添加图表错误处理和降级显示

2. **主题和样式系统**
   - **第1步**：分析 VSCode 主题 API 和 CSS 变量
   - **第2步**：实现动态主题适配机制
   - **第3步**：重构样式系统支持主题切换
   - **第4步**：实现自定义样式覆盖机制
   - **第5步**：优化字体和排版设置
   - **第6步**：实现代码高亮主题同步
   - **第7步**：添加主题预览和切换动画

3. **资源处理系统重构**
   - **第1步**：重构图片路径解析（适配 Webview 安全模型）
   - **第2步**：实现本地资源访问权限管理
   - **第3步**：添加网络资源加载策略
   - **第4步**：实现图片缩放和查看器
   - **第5步**：建立资源缓存和清理机制
   - **第6步**：优化大文件和多媒体资源处理

4. **安全性和性能优化**
   - **第1步**：实现内容安全策略（CSP）
   - **第2步**：添加 XSS 防护机制
   - **第3步**：优化 Webview 内存使用
   - **第4步**：实现资源加载优先级管理

### 第四阶段：性能优化和稳定性提升（2周）

1. **渲染性能优化**
   - **第1步**：实现虚拟滚动（大文档支持）
   - **第2步**：优化增量更新算法（DOM diff）
   - **第3步**：实现智能资源懒加载
   - **第4步**：建立多级渲染缓存系统
   - **第5步**：实现后台预渲染和预加载
   - **第6步**：添加渲染性能监控和分析

2. **内存和资源优化**
   - **第1步**：实现内存使用监控和报告
   - **第2步**：优化 Webview 生命周期管理
   - **第3步**：实现智能缓存策略（LRU、TTL）
   - **第4步**：添加资源自动清理机制
   - **第5步**：优化大文件处理策略
   - **第6步**：实现内存泄漏检测和预防

3. **用户体验优化**
   - **第1步**：优化界面响应速度（防抖、节流）
   - **第2步**：添加加载状态和进度指示
   - **第3步**：实现优雅的错误处理和恢复
   - **第4步**：添加操作反馈和确认机制
   - **第5步**：优化键盘导航和无障碍访问
   - **第6步**：实现用户偏好学习和适应

4. **稳定性和可靠性**
   - **第1步**：添加全面的错误捕获和日志记录
   - **第2步**：实现自动故障恢复机制
   - **第3步**：添加性能基准测试
   - **第4步**：实现健康检查和自诊断
   - **第5步**：建立错误报告和分析系统

### 第五阶段：测试、文档和发布准备（2周）

1. **全面测试体系建立**
   - **第1步**：建立单元测试框架
     - 预览面板生命周期测试
     - Markdown 渲染功能测试
     - 目录导航逻辑测试
     - 同步机制测试
   - **第2步**：实现集成测试
     - 端到端用户流程测试
     - 多文件切换场景测试
     - 主题和配置变更测试
     - 性能基准测试
   - **第3步**：兼容性测试
     - 多版本 VSCode 兼容性
     - 不同操作系统测试
     - 大文件和边界条件测试
   - **第4步**：用户验收测试
     - Beta 用户测试计划
     - 反馈收集和问题跟踪
     - 性能对比分析

2. **文档和知识传递**
   - **第1步**：更新用户文档
     - 功能使用指南
     - 配置选项说明
     - 故障排除指南
   - **第2步**：编写开发文档
     - 架构设计文档
     - API 参考文档
     - 扩展开发指南
   - **第3步**：创建示例和教程
     - 快速开始教程
     - 高级功能示例
     - 自定义配置案例

3. **发布准备和部署**
   - **第1步**：版本管理和打包
     - 语义化版本控制
     - 自动化构建流程
     - 发布包优化
   - **第2步**：迁移工具和向导
     - 自动配置迁移
     - 用户升级指南
     - 回滚机制验证
   - **第3步**：发布策略制定
     - 分阶段发布计划
     - 用户通知策略
     - 支持和反馈渠道

## 3. 详细实现计划

### 3.1 第一阶段：基础架构重构

#### 3.1.1 移除浏览器相关代码

```typescript
// 1. 删除文件
- src/browser/browserIntegration.ts
- webview/preview.js
- webview/markdown.css
- webview/preview.html

// 2. 修改 package.json
{
  "dependencies": {
    // 移除以下依赖
    "express": "^4.17.1",
    "ws": "^8.13.0",
    "open": "^8.4.0"
  }
}

// 3. 清理 HTTP 服务器代码
- src/server/markdownServer.ts
- src/server/websocketServer.ts
```

#### 3.1.2 创建预览面板模块

```typescript
// src/preview/MarkdownPreviewPanel.ts
import * as vscode from 'vscode';
import { MarkdownProcessor } from '../markdown/markdownProcessor';
import { TocProvider } from './TocProvider';

export class MarkdownPreviewPanel {
  private static instance: MarkdownPreviewPanel;
  private panel: vscode.WebviewPanel;
  private currentDocument: vscode.TextDocument | null = null;
  private markdownProcessor: MarkdownProcessor;
  private tocProvider: TocProvider;
  private disposables: vscode.Disposable[] = [];

  private constructor() {
    this.markdownProcessor = new MarkdownProcessor();
    this.tocProvider = new TocProvider();
  }

  public static getInstance(): MarkdownPreviewPanel {
    if (!MarkdownPreviewPanel.instance) {
      MarkdownPreviewPanel.instance = new MarkdownPreviewPanel();
    }
    return MarkdownPreviewPanel.instance;
  }

  public async show(document: vscode.TextDocument): Promise<void> {
    if (!this.panel) {
      this.createPanel();
    }
    this.currentDocument = document;
    await this.updateContent();
  }

  private createPanel(): void {
    this.panel = vscode.window.createWebviewPanel(
      'markdownPreview',
      'Markdown预览',
      vscode.ViewColumn.Beside,
      {
        enableScripts: true,
        retainContextWhenHidden: true,
        localResourceRoots: [
          vscode.Uri.file(path.join(this.context.extensionPath, 'media'))
        ]
      }
    );

    this.setupEventListeners();
  }

  private setupEventListeners(): void {
    // 面板关闭事件
    this.panel.onDidDispose(() => {
      this.dispose();
    });

    // 文档变更事件
    this.disposables.push(
      vscode.workspace.onDidChangeTextDocument(e => {
        if (e.document === this.currentDocument) {
          this.updateContent();
        }
      })
    );

    // 光标位置变化事件
    this.disposables.push(
      vscode.window.onDidChangeTextEditorSelection(e => {
        if (e.textEditor.document === this.currentDocument) {
          this.syncCursorPosition(e.selections[0].active);
        }
      })
    );

    // Webview 消息处理
    this.panel.webview.onDidReceiveMessage(
      message => {
        switch (message.type) {
          case 'ready':
            this.updateContent();
            break;
          case 'click':
            this.handleClick(message);
            break;
          case 'scroll':
            this.handleScroll(message);
            break;
        }
      },
      null,
      this.disposables
    );
  }

  private async updateContent(): Promise<void> {
    if (!this.currentDocument || !this.panel) {
      return;
    }

    const content = this.currentDocument.getText();
    const html = this.markdownProcessor.convertToHtml(content);
    const toc = this.tocProvider.generateToc(this.currentDocument);

    this.panel.webview.html = this.getWebviewContent(html, toc);
    this.panel.title = `预览: ${path.basename(this.currentDocument.fileName)}`;
  }

  private getWebviewContent(html: string, toc: TocItem[]): string {
    return `
      <!DOCTYPE html>
      <html>
        <head>
          <meta charset="UTF-8">
          <meta name="viewport" content="width=device-width, initial-scale=1.0">
          <link rel="stylesheet" href="${this.panel.webview.asWebviewUri(
            vscode.Uri.file(path.join(this.context.extensionPath, 'media', 'preview.css'))
          )}">
          <script src="https://cdn.jsdelivr.net/npm/mermaid@11.6.0/dist/mermaid.min.js"></script>
        </head>
        <body>
          <div class="container">
            <div id="toc-container">
              ${this.renderToc(toc)}
            </div>
            <div id="content-container">
              ${html}
            </div>
          </div>
          <script>
            // 初始化
            (function() {
              // 发送就绪消息
              vscode.postMessage({ type: 'ready' });
              
              // 初始化 Mermaid
              mermaid.initialize({ startOnLoad: true });
              
              // 设置事件监听
              setupEventListeners();
            })();
          </script>
        </body>
      </html>
    `;
  }

  public dispose(): void {
    this.disposables.forEach(d => d.dispose());
    this.panel?.dispose();
    this.panel = null;
    this.currentDocument = null;
  }
}
```

#### 3.1.3 调整配置项

```json
// package.json
{
  "contributes": {
    "configuration": {
      "properties": {
        "markdown-livesync.preview": {
          "type": "object",
          "properties": {
            "defaultView": {
              "type": "string",
              "enum": ["side", "window"],
              "default": "side",
              "description": "预览面板的默认显示位置"
            },
            "showToc": {
              "type": "boolean",
              "default": true,
              "description": "是否显示目录"
            },
            "syncScroll": {
              "type": "boolean",
              "default": true,
              "description": "是否同步滚动"
            }
          }
        },
        "markdown-livesync.theme": {
          "type": "object",
          "properties": {
            "fontSize": {
              "type": "number",
              "default": 14,
              "description": "预览字体大小"
            },
            "fontFamily": {
              "type": "string",
              "default": "",
              "description": "预览字体"
            },
            "lineHeight": {
              "type": "number",
              "default": 1.6,
              "description": "行高"
            }
          }
        },
        "markdown-livesync.performance": {
          "type": "object",
          "properties": {
            "chunkSize": {
              "type": "number",
              "default": 1000,
              "description": "分块渲染大小"
            },
            "cacheSize": {
              "type": "number",
              "default": 100,
              "description": "缓存大小"
            },
            "lazyLoad": {
              "type": "boolean",
              "default": true,
              "description": "是否启用懒加载"
            }
          }
        }
      }
    }
  }
}
```

### 3.2 第二阶段：核心功能迁移

#### 3.2.1 Markdown 渲染实现

```typescript
// src/markdown/MarkdownProcessor.ts
import * as MarkdownIt from 'markdown-it';
import * as hljs from 'highlight.js';
import * as katex from 'katex';

export class MarkdownProcessor {
  private md: MarkdownIt;
  
  constructor() {
    this.md = new MarkdownIt({
      html: true,
      linkify: true,
      typographer: true,
      highlight: (str, lang) => {
        if (lang && hljs.getLanguage(lang)) {
          try {
            return hljs.highlight(str, { language: lang }).value;
          } catch (__) {}
        }
        return '';
      }
    });

    this.setupPlugins();
  }

  private setupPlugins(): void {
    // 数学公式插件
    this.md.use(require('markdown-it-katex'));
    
    // 任务列表插件
    this.md.use(require('markdown-it-task-lists'));
    
    // 表格插件
    this.md.use(require('markdown-it-table'));
  }

  public convertToHtml(content: string): string {
    return this.md.render(content);
  }
}
```

#### 3.2.2 目录导航实现

```typescript
// src/preview/TocProvider.ts
import * as vscode from 'vscode';

export interface TocItem {
  level: number;
  text: string;
  line: number;
  children: TocItem[];
  isExpanded?: boolean;
}

export class TocProvider {
  private defaultCollapseLevel: number = 2;
  private config: vscode.WorkspaceConfiguration;

  constructor() {
    this.config = vscode.workspace.getConfiguration('markdown-livesync.toc');
    this.defaultCollapseLevel = this.config.get('defaultCollapseLevel', 2);
  }

  public generateToc(document: vscode.TextDocument): TocItem[] {
    const toc: TocItem[] = [];
    const lines = document.getText().split('\n');
    
    for (let i = 0; i < lines.length; i++) {
      const line = lines[i];
      const match = line.match(/^(#{1,6})\s+(.+)$/);
      
      if (match) {
        const level = match[1].length;
        const text = match[2];
        toc.push({
          level,
          text,
          line: i,
          children: [],
          isExpanded: level <= this.defaultCollapseLevel
        });
      }
    }
    
    return this.buildTocTree(toc);
  }

  private buildTocTree(items: TocItem[]): TocItem[] {
    const root: TocItem[] = [];
    const stack: TocItem[] = [];
    
    for (const item of items) {
      while (stack.length > 0 && stack[stack.length - 1].level >= item.level) {
        stack.pop();
      }
      
      if (stack.length === 0) {
        root.push(item);
      } else {
        stack[stack.length - 1].children.push(item);
      }
      
      stack.push(item);
    }
    
    return root;
  }

  public renderToc(toc: TocItem[]): string {
    return this.renderTocItems(toc, 0);
  }

  private renderTocItems(items: TocItem[], level: number): string {
    if (items.length === 0) {
      return '';
    }

    const html = items.map(item => {
      const hasChildren = item.children.length > 0;
      const toggleButton = hasChildren ? `
        <button class="toc-toggle ${item.isExpanded ? 'expanded' : ''}" 
                data-level="${item.level}"
                onclick="toggleTocItem(this)">
          <span class="toc-toggle-icon"></span>
        </button>
      ` : '';

      return `
        <div class="toc-item level-${level} ${item.isExpanded ? 'expanded' : 'collapsed'}">
          <div class="toc-item-header">
            ${toggleButton}
            <a href="#${this.slugify(item.text)}" 
               data-line="${item.line}"
               class="toc-item-link">
              ${item.text}
            </a>
          </div>
          ${hasChildren ? this.renderTocItems(item.children, level + 1) : ''}
        </div>
      `;
    }).join('');

    return `<div class="toc-level-${level}">${html}</div>`;
  }

  private slugify(text: string): string {
    return text
      .toLowerCase()
      .replace(/[^\w\u4e00-\u9fa5]+/g, '-')
      .replace(/(^-|-$)/g, '');
  }
}

// src/preview/toc.css
.toc-item {
  margin: 0;
  padding: 0;
  list-style: none;
}

.toc-item-header {
  display: flex;
  align-items: center;
  padding: 4px 8px;
  cursor: pointer;
}

.toc-toggle {
  width: 16px;
  height: 16px;
  margin-right: 4px;
  padding: 0;
  border: none;
  background: transparent;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
}

.toc-toggle-icon {
  width: 8px;
  height: 8px;
  border-right: 2px solid var(--vscode-foreground);
  border-bottom: 2px solid var(--vscode-foreground);
  transform: rotate(-45deg);
  transition: transform 0.2s;
}

.toc-toggle.expanded .toc-toggle-icon {
  transform: rotate(45deg);
}

.toc-item-link {
  color: var(--vscode-foreground);
  text-decoration: none;
  flex: 1;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.toc-item-link:hover {
  color: var(--vscode-textLink-foreground);
}

.toc-item.active > .toc-item-header > .toc-item-link {
  color: var(--vscode-textLink-activeForeground);
  font-weight: bold;
}

// src/preview/toc.js
function setupTocEvents() {
  // 目录项点击事件
  document.querySelectorAll('.toc-item-link').forEach(link => {
    link.addEventListener('click', (e) => {
      e.preventDefault();
      const line = parseInt(link.dataset.line);
      vscode.postMessage({
        type: 'toc-click',
        line: line
      });
    });
  });

  // 目录折叠/展开事件
  document.querySelectorAll('.toc-toggle').forEach(button => {
    button.addEventListener('click', (e) => {
      e.stopPropagation();
      toggleTocItem(button);
    });
  });
}

function toggleTocItem(button) {
  const item = button.closest('.toc-item');
  const isExpanded = item.classList.toggle('expanded');
  button.classList.toggle('expanded');
  
  // 保存折叠状态
  vscode.postMessage({
    type: 'toc-toggle',
    level: parseInt(button.dataset.level),
    isExpanded: isExpanded
  });
}

// 配置项
{
  "markdown-livesync.toc": {
    "defaultCollapseLevel": {
      "type": "number",
      "default": 2,
      "description": "目录默认折叠级别"
    },
    "showToggleButton": {
      "type": "boolean",
      "default": true,
      "description": "是否显示折叠按钮"
    },
    "highlightCurrentItem": {
      "type": "boolean",
      "default": true,
      "description": "是否高亮当前目录项"
    },
    "rememberCollapseState": {
      "type": "boolean",
      "default": true,
      "description": "是否记住目录折叠状态"
    }
  }
}
```

### 3.3 第三阶段：扩展功能迁移

#### 3.3.1 Mermaid 图表支持

```typescript
// src/preview/MermaidRenderer.ts
export class MermaidRenderer {
  private static instance: MermaidRenderer;
  
  private constructor() {
    this.initializeMermaid();
  }
  
  public static getInstance(): MermaidRenderer {
    if (!MermaidRenderer.instance) {
      MermaidRenderer.instance = new MermaidRenderer();
    }
    return MermaidRenderer.instance;
  }
  
  private async initializeMermaid() {
    await this.loadScript('https://cdn.jsdelivr.net/npm/mermaid@11.6.0/dist/mermaid.min.js');
    
    (window as any).mermaid.initialize({
      startOnLoad: true,
      theme: 'default',
      securityLevel: 'loose',
      flowchart: {
        useMaxWidth: true,
        htmlLabels: true
      }
    });
  }
  
  public async renderDiagrams() {
    const diagrams = document.querySelectorAll('.mermaid');
    for (const diagram of diagrams) {
      try {
        const id = `mermaid-${Math.random().toString(36).substr(2, 9)}`;
        const { svg } = await (window as any).mermaid.render(id, diagram.textContent || '');
        diagram.innerHTML = svg;
        
        // 添加全屏支持
        this.addFullscreenSupport(diagram);
      } catch (error) {
        console.error('Mermaid 渲染错误:', error);
        diagram.innerHTML = `<div class="mermaid-error">${error.message}</div>`;
      }
    }
  }

  private addFullscreenSupport(element: Element) {
    const button = document.createElement('button');
    button.className = 'mermaid-fullscreen';
    button.innerHTML = '⛶';
    button.onclick = () => this.toggleFullscreen(element);
    element.appendChild(button);
  }

  private toggleFullscreen(element: Element) {
    if (document.fullscreenElement) {
      document.exitFullscreen();
    } else {
      element.requestFullscreen();
    }
  }
}
```

### 3.4 第四阶段：性能优化

#### 3.4.1 渲染性能优化

```typescript
// src/preview/Renderer.ts
export class Renderer {
  private chunkSize: number;
  private cache: Map<string, string>;
  private cacheSize: number;
  
  constructor() {
    const config = vscode.workspace.getConfiguration('markdown-livesync.performance');
    this.chunkSize = config.get('chunkSize', 1000);
    this.cacheSize = config.get('cacheSize', 100);
    this.cache = new Map();
  }

  public async render(content: string): Promise<string> {
    // 检查缓存
    const cacheKey = this.getCacheKey(content);
    if (this.cache.has(cacheKey)) {
      return this.cache.get(cacheKey)!;
    }

    // 分块渲染
    const chunks = this.splitContent(content);
    const renderedChunks = await Promise.all(
      chunks.map(chunk => this.renderChunk(chunk))
    );

    const result = renderedChunks.join('');
    
    // 更新缓存
    this.updateCache(cacheKey, result);
    
    return result;
  }

  private splitContent(content: string): string[] {
    const chunks: string[] = [];
    const lines = content.split('\n');
    
    for (let i = 0; i < lines.length; i += this.chunkSize) {
      chunks.push(lines.slice(i, i + this.chunkSize).join('\n'));
    }
    
    return chunks;
  }

  private async renderChunk(chunk: string): Promise<string> {
    // 使用 Web Worker 进行渲染
    return new Promise((resolve) => {
      const worker = new Worker('renderWorker.js');
      worker.postMessage(chunk);
      worker.onmessage = (e) => resolve(e.data);
    });
  }

  private updateCache(key: string, value: string): void {
    if (this.cache.size >= this.cacheSize) {
      // 移除最旧的缓存
      const firstKey = this.cache.keys().next().value;
      this.cache.delete(firstKey);
    }
    this.cache.set(key, value);
  }

  private getCacheKey(content: string): string {
    return crypto.createHash('md5').update(content).digest('hex');
  }
}
```

### 3.5 第五阶段：测试和文档

#### 3.5.1 单元测试

```typescript
// src/test/preview.test.ts
import * as assert from 'assert';
import * as vscode from 'vscode';
import { MarkdownPreviewPanel } from '../preview/MarkdownPreviewPanel';

suite('Markdown Preview Test Suite', () => {
  test('创建预览面板', async () => {
    const panel = MarkdownPreviewPanel.getInstance();
    assert.ok(panel);
  });

  test('渲染 Markdown 内容', async () => {
    const panel = MarkdownPreviewPanel.getInstance();
    const document = await vscode.workspace.openTextDocument({
      content: '# 测试标题\n\n测试内容'
    });
    await panel.show(document);
    // 验证渲染结果
  });

  test('目录生成', async () => {
    const tocProvider = new TocProvider();
    const document = await vscode.workspace.openTextDocument({
      content: '# 标题1\n## 标题2\n### 标题3'
    });
    const toc = tocProvider.generateToc(document);
    assert.strictEqual(toc.length, 3);
  });
});
```

#### 3.5.2 性能测试

```typescript
// src/test/performance.test.ts
import * as assert from 'assert';
import { Renderer } from '../preview/Renderer';

suite('Performance Test Suite', () => {
  test('大文件渲染性能', async () => {
    const renderer = new Renderer();
    const largeContent = '# 测试\n'.repeat(10000);
    
    const startTime = Date.now();
    await renderer.render(largeContent);
    const endTime = Date.now();
    
    assert.ok(endTime - startTime < 2000); // 2秒内完成
  });

  test('缓存性能', async () => {
    const renderer = new Renderer();
    const content = '# 测试内容';
    
    // 首次渲染
    const firstRenderStart = Date.now();
    await renderer.render(content);
    const firstRenderTime = Date.now() - firstRenderStart;
    
    // 缓存渲染
    const cacheRenderStart = Date.now();
    await renderer.render(content);
    const cacheRenderTime = Date.now() - cacheRenderStart;
    
    assert.ok(cacheRenderTime < firstRenderTime / 10); // 缓存渲染至少快10倍
  });
});
```

## 4. 测试计划

### 4.1 单元测试

1. **预览面板测试**
   - 测试面板创建和销毁
   - 测试文档变更监听
   - 测试光标同步

2. **目录导航测试**
   - 测试目录生成
   - 测试目录树构建
   - 测试目录交互

3. **渲染功能测试**
   - 测试 Markdown 渲染
   - 测试 Mermaid 图表渲染
   - 测试代码高亮

### 4.2 集成测试

1. **功能集成测试**
   - 测试完整预览流程
   - 测试实时同步
   - 测试主题切换

2. **性能测试**
   - 测试大文件渲染
   - 测试实时更新性能
   - 测试内存占用

## 5. 发布计划

### 5.1 版本规划

1. **v1.0.0-alpha**
   - 基础预览功能
   - 目录导航
   - 实时同步

2. **v1.0.0-beta**
   - Mermaid 图表支持
   - 代码高亮
   - 主题支持

3. **v1.0.0**
   - 完整功能
   - 性能优化
   - 文档完善

### 5.2 发布检查清单

- [ ] 功能测试通过
- [ ] 性能测试达标
- [ ] 文档更新完成
- [ ] 示例代码准备
- [ ] 发布说明编写
- [ ] 版本号更新
- [ ] 打包发布

## 6. 回滚计划

### 6.1 回滚触发条件

1. 发现严重 bug
2. 性能问题
3. 用户反馈问题

### 6.2 回滚步骤

1. 停止新版本发布
2. 回退到上一个稳定版本
3. 通知用户
4. 收集问题反馈
5. 修复问题
6. 重新发布

## 7. 优化后的时间规划

### 7.1 调整后的阶段安排

1. **第零阶段**：1周（新增）
   - 重构准备和风险评估
   - 代码备份和分支管理
   - 兼容性基线建立

2. **第一阶段**：2周
   - 基础架构重构
   - 渐进式移除旧代码
   - 新预览面板基础实现

3. **第二阶段**：2.5周（调整）
   - 核心功能迁移
   - Markdown渲染系统重构
   - 目录导航和同步系统

4. **第三阶段**：2.5周（调整）
   - 扩展功能迁移
   - Mermaid图表和主题系统
   - 安全性和性能优化

5. **第四阶段**：2周（调整）
   - 深度性能优化
   - 稳定性提升
   - 内存和资源管理

6. **第五阶段**：2周（调整）
   - 全面测试和文档
   - 发布准备和部署
   - 用户迁移工具

**总计：10周**（原计划7周 → 优化后10周）

### 7.2 时间调整理由

**增加缓冲时间的必要性**
- 大型重构项目的复杂性通常被低估
- 需要充分的测试和验证时间
- 用户反馈和问题修复需要额外时间

**风险缓解考虑**
- 每个阶段增加20%的缓冲时间
- 关键路径识别和优先级调整
- 并行任务的依赖关系管理

### 7.3 里程碑和检查点

**第零阶段里程碑**
- [ ] 重构分支创建完成
- [ ] 风险评估报告完成
- [ ] 回滚策略验证通过

**第一阶段里程碑**
- [ ] 新预览面板基础功能可用
- [ ] 旧系统功能开关实现
- [ ] 配置迁移工具完成

**第二阶段里程碑**
- [ ] Markdown渲染功能对等
- [ ] 目录导航功能完整
- [ ] 基础同步机制工作正常

**第三阶段里程碑**
- [ ] Mermaid图表功能完整
- [ ] 主题系统集成完成
- [ ] 安全性验证通过

**第四阶段里程碑**
- [ ] 性能指标达到预期
- [ ] 内存使用优化完成
- [ ] 稳定性测试通过

**第五阶段里程碑**
- [ ] 所有测试用例通过
- [ ] 文档更新完成
- [ ] 发布包准备就绪

## 8. 风险评估

### 8.1 技术风险

1. **性能问题**
   - 风险：大文件渲染性能
   - 缓解：实现分块渲染

2. **兼容性问题**
   - 风险：VSCode 版本兼容
   - 缓解：明确版本要求

### 8.2 项目风险

1. **进度风险**
   - 风险：功能迁移延迟
   - 缓解：设置缓冲时间

2. **质量风险**
   - 风险：功能不完整
   - 缓解：加强测试

## 9. 重构计划分析和优化建议

### 9.1 重构目标完整性评估

#### ✅ 已明确的优势
- 核心目标清晰：从浏览器预览迁移到VSCode内置预览
- 功能覆盖全面：12个主要功能模块详细列出
- 技术方向正确：利用VSCode Webview API提升集成度

#### ⚠️ 发现的问题和改进建议

**目标可衡量性不足**
- **问题**：缺少具体的性能指标和用户体验指标
- **建议**：添加以下量化目标
  - 渲染时间：大文件（>1MB）渲染时间 < 2秒
  - 内存占用：预览面板内存使用 < 100MB
  - 响应时间：编辑器到预览同步延迟 < 100ms
  - 兼容性：支持VSCode 1.60.0+版本

**目标冲突识别**
- **问题**：单窗口多文件预览与独立窗口功能可能冲突
- **建议**：明确两种模式的切换机制和状态管理策略

**遗漏的关键目标**
- **数据迁移**：用户配置、缓存数据的平滑迁移
- **安全性**：XSS防护、内容安全策略（CSP）
- **可访问性**：键盘导航、屏幕阅读器支持
- **国际化**：多语言界面支持

### 9.2 重构步骤详细性分析

#### ✅ 步骤逻辑优势
- 五阶段划分合理，从基础到扩展的顺序正确
- 每个阶段都有明确的交付物

#### ⚠️ 改进建议

**增加第零阶段：重构准备**
- **必要性**：大型重构需要充分准备，降低风险
- **内容**：代码备份、依赖分析、兼容性基线、应急预案

**细化步骤粒度**
- **问题**：原步骤过于宏观，缺少具体的实施细节
- **改进**：将每个大步骤分解为6-8个具体的子步骤

**添加验收标准**
- **问题**：缺少每个步骤的完成标准
- **建议**：为每个子步骤定义明确的输入、输出和验收条件

### 9.3 实施计划可行性评估

#### ⚠️ 时间估算问题
- **原计划**：7周总时长
- **评估结果**：时间估算过于乐观
- **建议调整**：
  - 第零阶段：1周（新增）
  - 第一阶段：2周 → 2周（保持）
  - 第二阶段：2周 → 2.5周（增加）
  - 第三阶段：2周 → 2.5周（增加）
  - 第四阶段：1周 → 2周（增加）
  - 第五阶段：1周 → 2周（增加）
  - **总计**：7周 → 10周

#### 🔄 风险缓解策略

**技术风险**
- **Webview API限制**：提前验证关键功能的可行性
- **性能问题**：建立性能基准，持续监控
- **兼容性问题**：多版本测试，降级策略

**项目风险**
- **进度延迟**：设置20%的缓冲时间
- **质量问题**：每个阶段都有质量门禁
- **用户接受度**：Beta测试和用户反馈循环

### 9.4 技术细节补充建议

#### 🔧 架构设计优化

**模块化设计**
```typescript
// 建议的模块结构
src/
├── core/                 # 核心模块
│   ├── extension.ts      # 插件入口
│   ├── lifecycle.ts      # 生命周期管理
│   └── events.ts         # 事件系统
├── preview/              # 预览系统
│   ├── panel.ts          # 预览面板
│   ├── renderer.ts       # 渲染引擎
│   ├── sync.ts           # 同步机制
│   └── webview/          # Webview资源
├── markdown/             # Markdown处理
│   ├── processor.ts      # 处理器
│   ├── plugins/          # 插件系统
│   └── themes/           # 主题系统
├── toc/                  # 目录导航
│   ├── provider.ts       # 目录提供者
│   ├── renderer.ts       # 目录渲染
│   └── navigation.ts     # 导航逻辑
├── config/               # 配置管理
│   ├── manager.ts        # 配置管理器
│   ├── migration.ts      # 配置迁移
│   └── validation.ts     # 配置验证
└── utils/                # 工具函数
    ├── performance.ts    # 性能工具
    ├── security.ts       # 安全工具
    └── logger.ts         # 日志系统
```

**性能优化策略**
- **虚拟滚动**：处理大文档时的性能问题
- **增量渲染**：只更新变化的部分
- **智能缓存**：多级缓存策略
- **资源懒加载**：按需加载图片和资源

**安全性增强**
- **内容安全策略**：严格的CSP配置
- **输入验证**：防止XSS攻击
- **资源隔离**：安全的本地资源访问

### 9.5 测试策略完善

#### 🧪 测试金字塔

**单元测试（70%）**
- 核心逻辑测试
- 工具函数测试
- 组件独立测试

**集成测试（20%）**
- 模块间交互测试
- API集成测试
- 端到端流程测试

**UI测试（10%）**
- 用户界面测试
- 交互行为测试
- 视觉回归测试

#### 📊 性能基准测试
- **渲染性能**：不同大小文档的渲染时间
- **内存使用**：长时间使用的内存变化
- **响应性能**：用户操作的响应时间
- **资源消耗**：CPU和磁盘使用情况

### 9.6 发布和部署策略

#### 🚀 分阶段发布计划

**Alpha版本（内部测试）**
- 基础功能验证
- 核心团队测试
- 主要bug修复

**Beta版本（用户测试）**
- 邀请活跃用户参与
- 收集使用反馈
- 性能优化调整

**正式版本（全量发布）**
- 完整功能发布
- 用户迁移指南
- 技术支持准备

#### 📋 发布检查清单

**功能完整性**
- [ ] 所有计划功能已实现
- [ ] 核心用例测试通过
- [ ] 性能指标达标
- [ ] 兼容性测试通过

**质量保证**
- [ ] 代码审查完成
- [ ] 自动化测试通过
- [ ] 安全扫描通过
- [ ] 文档更新完成

**用户体验**
- [ ] 用户界面友好
- [ ] 错误处理完善
- [ ] 帮助文档齐全
- [ ] 迁移工具可用

## 10. 后续计划和长期维护

### 10.1 功能增强路线图

**短期计划（3个月）**
- 用户反馈收集和问题修复
- 性能优化和稳定性提升
- 小功能增强和改进

**中期计划（6个月）**
- 更多Markdown扩展支持
- 高级自定义选项
- 协作功能探索

**长期计划（1年）**
- AI辅助功能集成
- 云端同步支持
- 移动端适配

### 10.2 维护和支持策略

**定期维护**
- 依赖更新和安全补丁
- 性能监控和优化
- 用户反馈处理

**社区建设**
- 开发者文档完善
- 插件生态建设
- 用户社区运营

**技术债务管理**
- 代码质量持续改进
- 架构演进规划
- 技术栈升级策略